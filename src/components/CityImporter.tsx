import { useState, useRef } from "react"
import { useMutation } from "convex/react"
import { api } from "../../convex/_generated/api"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Upload, FileSpreadsheet, Database } from "lucide-react"

export function CityImporter() {
  const [file, setFile] = useState<File | null>(null)
  const [csvData, setCsvData] = useState("")
  const [format, setFormat] = useState<"csv" | "json">("csv")
  const [clearExisting, setClearExisting] = useState(false)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const importCities = useMutation(api.importCitiesScript.importCitiesFromData)
  const importSample = useMutation(api.importCitiesScript.importSampleCities)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setError(null)
      
      // Read file content
      const reader = new FileReader()
      reader.onload = (event) => {
        const content = event.target?.result as string
        if (selectedFile.name.endsWith('.csv')) {
          setCsvData(content)
          setFormat("csv")
        } else if (selectedFile.name.endsWith('.json')) {
          setCsvData(content)
          setFormat("json")
        } else {
          setError("Please select a CSV or JSON file")
        }
      }
      reader.readAsText(selectedFile)
    }
  }

  const handleImport = async () => {
    if (!csvData.trim()) {
      setError("Please select a file or enter data manually")
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await importCities({
        data: csvData,
        format,
        clearExisting,
      })
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  const handleImportSample = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await importSample({})
      setResult(response)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setLoading(false)
    }
  }

  const clearForm = () => {
    setFile(null)
    setCsvData("")
    setResult(null)
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            City Data Importer
          </CardTitle>
          <CardDescription>
            Import cities from Excel/CSV files or JSON data into your database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload Section */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="file-upload" className="text-base font-medium">
                Upload File
              </Label>
              <div className="mt-2 flex items-center gap-4">
                <Input
                  ref={fileInputRef}
                  id="file-upload"
                  type="file"
                  accept=".csv,.json,.xlsx,.xls"
                  onChange={handleFileChange}
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  Browse
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Supported formats: CSV, JSON. Excel files should be saved as CSV first.
              </p>
            </div>

            {file && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium">{file.name}</span>
                  <span className="text-xs text-gray-500">
                    ({(file.size / 1024).toFixed(1)} KB)
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Manual Data Entry */}
          <div className="space-y-4">
            <Label htmlFor="csv-data" className="text-base font-medium">
              Or Enter Data Manually
            </Label>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="format">Data Format</Label>
                <Select value={format} onValueChange={(value: "csv" | "json") => setFormat(value)}>
                  <SelectTrigger id="format">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="json">JSON</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Textarea
              id="csv-data"
              value={csvData}
              onChange={(e) => setCsvData(e.target.value)}
              placeholder={
                format === "csv"
                  ? "name,alias,estado\nMexico City,CDMX,Ciudad de Mexico\nGuadalajara,GDL,Jalisco"
                  : '[{"name":"Mexico City","alias":"CDMX","estado":"Ciudad de Mexico"}]'
              }
              rows={8}
              className="font-mono text-sm"
            />
          </div>

          {/* Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="clear-existing"
                checked={clearExisting}
                onCheckedChange={(checked) => setClearExisting(checked as boolean)}
              />
              <Label htmlFor="clear-existing" className="text-sm">
                Clear existing cities before importing (⚠️ This will delete all current cities)
              </Label>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button
              onClick={handleImport}
              disabled={loading || !csvData.trim()}
              className="flex-1"
            >
              {loading ? "Importing..." : "Import Cities"}
            </Button>
            <Button
              variant="outline"
              onClick={handleImportSample}
              disabled={loading}
            >
              Import Sample Data
            </Button>
            <Button variant="outline" onClick={clearForm}>
              Clear
            </Button>
          </div>

          {/* Results */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {result && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Import Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm">
                    <span className="font-medium">Status:</span>{" "}
                    <span className={result.success ? "text-green-600" : "text-red-600"}>
                      {result.success ? "Success" : "Failed"}
                    </span>
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">Message:</span> {result.message}
                  </p>
                  {result.total && (
                    <>
                      <p className="text-sm">
                        <span className="font-medium">Total processed:</span> {result.total}
                      </p>
                      <p className="text-sm">
                        <span className="font-medium">Successfully imported:</span>{" "}
                        <span className="text-green-600">{result.imported}</span>
                      </p>
                      {result.failed > 0 && (
                        <p className="text-sm">
                          <span className="font-medium">Failed:</span>{" "}
                          <span className="text-red-600">{result.failed}</span>
                        </p>
                      )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Format Help */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Expected Data Format</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label className="font-medium">CSV Format:</Label>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
{`name,alias,estado
Mexico City,CDMX,Ciudad de Mexico
Guadalajara,GDL,Jalisco
Monterrey,MTY,Nuevo Leon`}
                </pre>
              </div>
              <div>
                <Label className="font-medium">JSON Format:</Label>
                <pre className="text-xs bg-gray-100 p-2 rounded mt-1">
{`[
  {"name":"Mexico City","alias":"CDMX","estado":"Ciudad de Mexico"},
  {"name":"Guadalajara","alias":"GDL","estado":"Jalisco"}
]`}
                </pre>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  )
}
