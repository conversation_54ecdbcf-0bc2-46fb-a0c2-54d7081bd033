{"name": "vite-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.31.9", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "lucide-react": "^0.514.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/node": "^24.0.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "tw-animate-css": "^1.3.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}