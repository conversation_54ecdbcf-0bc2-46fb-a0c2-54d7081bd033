import { httpRouter } from "convex/server";
import { httpAction } from "../_generated/server";
import { api } from "../_generated/api";


const http = httpRouter();


http.route({
  path: "/addFlete",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const { cliente , id } = await request.json();

    await ctx.runMutation(api.fletes.addFlete, {
      cliente,
      id,
    });

    return new Response(null, {
      status: 200,
    });
  })

});



// Convex expects the router to be the default export of `convex/http.js`.
export default http;