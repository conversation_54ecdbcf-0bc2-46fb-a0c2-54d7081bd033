import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";


export const addFlete = mutation({
  args: {
    cliente: v.string(),
    fleteId: v.float64(),
    unidad: v.id("unidades"),
  },
  handler: async (ctx, args) => {
    const { cliente, fleteId, unidad } = args;

    return await ctx.db.insert("fletes", {
      cliente,
      fleteId,
      unidad,
    });
  },
});

// Google Maps Distance Matrix API action
export const getRouteDistance = action({
  args: {
    origin: v.string(),
    destination: v.string(),
    units: v.optional(v.union(v.literal("metric"), v.literal("imperial"))),
  },
  handler: async (ctx, args) => {
    const { origin, destination, units = "metric" } = args;

    // Get Google Maps API key from environment variables
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      throw new Error("Google Maps API key not found. Please set GOOGLE_MAPS_API_KEY environment variable.");
    }

    // Build the API URL
    const baseUrl = "https://maps.googleapis.com/maps/api/distancematrix/json";
    const params = new URLSearchParams({
      origins: origin,
      destinations: destination,
      units: units,
      key: apiKey,
    });

    try {
      // Make the API call
      const response = await fetch(`${baseUrl}?${params}`);

      if (!response.ok) {
        throw new Error(`Google Maps API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Check if the API returned an error
      if (data.status !== "OK") {
        throw new Error(`Google Maps API error: ${data.status} - ${data.error_message || "Unknown error"}`);
      }

      // Extract the distance and duration from the response
      const element = data.rows[0]?.elements[0];

      if (!element || element.status !== "OK") {
        throw new Error(`Route not found between ${origin} and ${destination}`);
      }

      return {
        origin: data.origin_addresses[0],
        destination: data.destination_addresses[0],
        distance: {
          text: element.distance.text,
          value: element.distance.value, // Distance in meters
        },
        duration: {
          text: element.duration.text,
          value: element.duration.value, // Duration in seconds
        },
        status: "OK",
      };

    } catch (error) {
      console.error("Error calling Google Maps API:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      throw new Error(`Failed to get route distance: ${errorMessage}`);
    }
  },
});

// Helper query to find a city by name
export const getCityByName = query({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("ciudades")
      .filter((q) => q.eq(q.field("name"), args.name))
      .first();
  },
});

