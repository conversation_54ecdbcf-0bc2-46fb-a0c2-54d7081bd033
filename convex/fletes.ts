import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const addFlete = mutation({
  args: {
    cliente: v.string(),
    fleteId: v.float64(),
    unidad: v.id("unidades"),
  },
  handler: async (ctx, args) => {
    const { cliente, fleteId, unidad } = args;

    return await ctx.db.insert("fletes", {
      cliente,
      fleteId,
      unidad,
    });
  },
});

export const getFletesAnterioresUnidad = query({
  args: {
    numberOfFletes: v.number(),
    unidad: v.string(),
  },
  handler: async (ctx, args) => {
    const { numberOfFletes, unidad } = args;
    await ctx.db.query("unidades")
      .filter(q => q.eq("eco", unidad))
      .unique();

    await ctx.db.query("fletes")
      .withIndex("by_unidad", (q) => q.eq("unidad", channel))
      .take(numberOfFletes);

    return await ctx.db.query("fletes")..collect();
  },
});

