import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";
import {Id} from "./_generated/dataModel";

// Action to import cities from a CSV string or JSON array
export const importCitiesFromData = internalAction({
  args: {
    data: v.string(), // CSV string or JSON string
    format: v.union(v.literal("csv"), v.literal("json")),
    clearExisting: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const { data, format, clearExisting = false } = args;

    try {
      // Clear existing cities if requested
      if (clearExisting) {
        console.log("Clearing existing cities...");
        await ctx.runMutation(internal.importCities.clearAllCities, {});
      }

      let cities: Array<{ name: string; alias: string; estado: Id<"estados"> }> = [];

        // Parse JSON data
        cities = JSON.parse(data);

      if (cities.length === 0) {
        return { success: false, message: "No cities found in data" };
      }

      // Import cities in batches
      const BATCH_SIZE = 50;
      let totalImported = 0;
      let totalFailed = 0;

      for (let i = 0; i < cities.length; i += BATCH_SIZE) {
        const batch = cities.slice(i, i + BATCH_SIZE);
        console.log(`Importing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(cities.length / BATCH_SIZE)}`);

        const result = await ctx.runMutation(internal.importCities.batchCreateCities, {
          cities: batch
        });

        totalImported += result.successful;
        totalFailed += result.failed;
      }

      return {
        success: true,
        total: cities.length,
        imported: totalImported,
        failed: totalFailed,
        message: `Successfully imported ${totalImported} cities, ${totalFailed} failed`
      };

    } catch (error) {
      console.error("Error importing cities:", error);
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      };
    }
  },
});

