import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";
import {Id} from "./_generated/dataModel";

// Action to import cities from a JSON array
export const importStatesFromData = internalAction({
  args: {
    data: v.string(), //JSON string
  },
  handler: async (ctx, args) => {
    const { data} = args;

    try {

      let states: Array<{ name: string; alias: string; }> = [];

        // Parse JSON data
        states = JSON.parse(data);

      if (states.length === 0) {
        return { success: false, message: "No states found in data" };
      }

      // Import cities in batches
      const BATCH_SIZE = 50;
      let totalImported = 0;
      let totalFailed = 0;

      for (let i = 0; i < states.length; i += BATCH_SIZE) {
        const batch = states.slice(i, i + BATCH_SIZE);
        console.log(`Importing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(states.length / BATCH_SIZE)}`);

        const result = await ctx.runMutation(internal.importCities.batchCreateCities, {
          states: batch
        });

        totalImported += result.successful;
        totalFailed += result.failed;
      }

      return {
        success: true,
        total: states.length,
        imported: totalImported,
        failed: totalFailed,
        message: `Successfully imported ${totalImported} cities, ${totalFailed} failed`
      };

    } catch (error) {
      console.error("Error importing cities:", error);
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      };
    }
  },
});

