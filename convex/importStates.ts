import { internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import {Id} from "./_generated/dataModel";

// Internal mutation to create a single city
export const createState = internalMutation({
  args: {
    name: v.string(),
    alias: v.string(),
  },
  handler: async (ctx, args) => {

    const StateId = await ctx.db.insert("estados", {
      name: args.name,
      alias: args.alias,
    });

    console.log(`Created state: ${args.name}, ${args.alias}`);
    return StateId;
  },
});

// Internal mutation to batch create cities
export const batchCreateStates = internalMutation({
  args: {
    states: v.array(v.object({
      name: v.string(),
      alias: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const results = [];
    
    for (const state of args.states) {
      try {
        const stateId: Id<"estados"> = await ctx.runMutation(internal.importStates.createState, state);
        results.push({ success: true, stateId, state });
      } catch (error) {
        console.error(`Error creating state ${state.name}:`, error);
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        results.push({ success: false, error: errorMessage, state });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`Batch import completed: ${successful} successful, ${failed} failed`);
    return {
      total: args.states.length,
      successful,
      failed,
      results
    };
  },
});
